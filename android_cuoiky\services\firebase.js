// firebaseConfig.js
import { initializeApp } from "firebase/app";
import { initializeAuth, getReactNativePersistence, getAuth } from "firebase/auth";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { getFirestore } from "firebase/firestore";

const firebaseConfig = {
  apiKey: "AIzaSyAViKZsh7jsWjUFWmapzCElIM101kjz5e4",
  authDomain: "social-media-d5baa.firebaseapp.com",
  projectId: "social-media-d5baa",
  storageBucket: "social-media-d5baa.appspot.com",
  messagingSenderId: "48168767870",
  appId: "1:48168767870:web:59556159a576a3498d632f",
  measurementId: "G-Q9V3FCLFW0"
};

const app = initializeApp(firebaseConfig);

let auth;
try {
  auth = getAuth(app);
} catch (error) {
  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
  });
}

const db = getFirestore(app);

export { auth, db };
