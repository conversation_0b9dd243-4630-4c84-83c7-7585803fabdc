/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Endpoint } from '../../../src/api';
import { Route } from '../mock_fetch';
export declare function endpointUrl(endpoint: Endpoint): string;
export declare function endpointUrlWithParams(endpoint: Endpoint, params: Record<string, any>): string;
export declare function mockEndpoint(endpoint: Endpoint, response: object, status?: number): Route;
export declare function mockEndpointWithParams(endpoint: Endpoint, params: Record<string, any>, response: object, status?: number): Route;
