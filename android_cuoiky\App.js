import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View } from 'react-native';
import RegisterScreen from './screens/RegisterScreen';
import { auth } from './services/firebase';
import { useEffect, useState } from 'react';

export default function App() {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Ensure Firebase Auth is initialized
    const initializeAuth = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 500)); // Give time for native initialization
        setIsReady(true);
      } catch (error) {
        console.error('Firebase initialization error:', error);
      }
    };

    initializeAuth();
  }, []);

  if (!isReady) {
    return (
      <View style={styles.container}>
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <RegisterScreen />
      <StatusBar style="auto" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
